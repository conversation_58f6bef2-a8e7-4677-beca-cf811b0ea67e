;;; gemini.el --- Gemini provider for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides the Gemini provider implementation for the AI Auto Complete package.

;;; Code:

(require 'request)
(require 'json)
(require 's)
(require 'core/backend)
(require 'customization/model-management)

;; Helper function to parse the 'tool-result' string from history
;; This is a simplified parser; a more robust system would store structured tool data.
(defun ai-auto-complete-gemini--parse-internal-tool-result (tool-result-string)
  "Robustly parse a string potentially containing multiple <tool_result> XML blocks.
Returns a list of (cons tool-name . tool-content-string), or nil if input is empty."
  (let ((results '())
        (start 0)
        (trimmed-input (s-trim tool-result-string))) ; Trim once at the beginning

    ;; If the input is empty after trimming, return nil immediately.
    (if (string-empty-p trimmed-input)
        nil
      (progn
        ;; Attempt to parse <tool_result...> tags
        ;; Regex updated for non-greedy multi-line content within tool_result
        (while (string-match "<tool_result name=\"\\([^\"]+\\)\">[[:space:]]*\\(\\(?:.\\|\n\\)*?\\)[[:space:]]*</tool_result>" trimmed-input start)
          (let ((tool-name (match-string 1 trimmed-input))
                (tool-content (s-trim (match-string 2 trimmed-input)))) ; Content inside tool_result
            (push (cons tool-name tool-content) results))
          (setq start (match-end 0)))

        ;; If no <tool_result> tags were found by the primary regex,
        ;; and results list is still empty, try the simpler "Tool: name\nResult: content" format.
        ;; This part assumes only one such block if this format is used.
        (when (null results)
          (if (string-match "Tool: \\([^\n]+\\)\nResult: \\(\\(?:.\\|\n\\)*\\)" trimmed-input) ; Match multi-line result
              (push (cons (s-trim (match-string 1 trimmed-input))
                          (s-trim (match-string 2 trimmed-input)))
                    results)))
        
        ;; If still no results from specific parsers, and the input was not empty,
        ;; treat the whole original trimmed string as output of an "unknown_tool_operation".
        ;; This ensures that if there was content, we try to send something.
        (when (and (null results) (not (string-empty-p trimmed-input)))
            (push (cons "unknown_tool_operation" trimmed-input) results))
            
        (nreverse results))))) ; Return in the order they appeared, or nil if input was empty.

;; Helper function to build the 'contents' array for Gemini
(defun ai-auto-complete-gemini--build-contents (history current-user-context)
  "Build the 'contents' array for Gemini API from internal HISTORY and CURRENT-USER-CONTEXT."
  (message "GEMINI-BUILD-CONTENTS: FUNCTION CALLED! History length: %d, Context: %s"
           (length history)
           (substring current-user-context 0 (min 100 (length current-user-context))))
  (message "GEMINI-BUILD-CONTENTS: Full context length: %d, contains 'Conversation history': %s"
           (length current-user-context)
           (if (string-match-p "Conversation history:" current-user-context) "YES" "NO"))
  (let ((gemini-contents '())
        (actual-history history)
        (clean-user-context current-user-context))

    ;; If we're in chat mode and history is empty, try to get the actual chat history
    (when (and (boundp 'ai-auto-complete-chat-mode)
               ai-auto-complete-chat-mode
               (or (null history) (= (length history) 0))
               (boundp 'ai-auto-complete--chat-history)
               ai-auto-complete--chat-history)
      (setq actual-history ai-auto-complete--chat-history)
      (message "DEBUG: Using actual chat history with %d messages" (length actual-history)))

    ;; If we're in chat mode with history, we should use the history and NOT add context as user message
    (when (and (boundp 'ai-auto-complete-chat-mode)
               ai-auto-complete-chat-mode
               actual-history
               (> (length actual-history) 0))
      (message "DEBUG: In chat mode with %d history messages, will NOT add context as user message" (length actual-history))
      (setq clean-user-context nil))

    ;; Clean the user context - remove system prompt if it's been concatenated
    ;; This handles the case where chat.el concatenates system prompt with user input
    (when (and (boundp 'ai-auto-complete-chat-mode)
               ai-auto-complete-chat-mode
               (stringp current-user-context))
      ;; Check if the context looks like a system prompt (starts with "You are" and contains "Conversation history:")
      ;; If so, and we have actual history, don't use the context as a user message
      (let ((starts-with-you-are (string-match-p "^You are" current-user-context))
            (contains-conv-history (string-match-p "Conversation history:" current-user-context))
            (has-history (and actual-history (> (length actual-history) 0))))
        (message "DEBUG: Pattern check - starts with 'You are': %s, contains 'Conversation history': %s, has history: %s"
                 (if starts-with-you-are "YES" "NO")
                 (if contains-conv-history "YES" "NO")
                 (if has-history "YES" "NO"))
        (if (and starts-with-you-are contains-conv-history has-history)
            (progn
              (message "DEBUG: Context appears to be system prompt + history, not using as user message")
              (setq clean-user-context nil)) ; Don't add context as user message
        ;; Otherwise, try to extract user message from combined context
        (cond
         ;; Pattern: "System: <prompt>\n\nUser: <message>"
         ((string-match "^.*?System: .*?\n\nUser: \\(.*\\)$" current-user-context)
          (setq clean-user-context (match-string 1 current-user-context))
          (message "DEBUG: Extracted user message from combined context: %s"
                   (substring clean-user-context 0 (min 50 (length clean-user-context)))))
         ;; Pattern: system prompt followed by history and current message
         ((string-match "\n\nConversation history:\n.*\n\n\\([^:]+\\)$" current-user-context)
          (setq clean-user-context (match-string 1 current-user-context))
          (message "DEBUG: Extracted user message from context with history: %s"
                   (substring clean-user-context 0 (min 50 (length clean-user-context)))))
         ;; If context starts with a system prompt pattern, try to extract just the user part
         ((string-match "^You are .*?\n\n\\(.*\\)$" current-user-context)
          (setq clean-user-context (match-string 1 current-user-context))
          (message "DEBUG: Extracted user message from system+user context: %s"
                   (substring clean-user-context 0 (min 50 (length clean-user-context)))))
         ;; Otherwise use the context as-is
         (t
          (message "DEBUG: Using context as-is: %s"
                   (substring current-user-context 0 (min 50 (length current-user-context))))))))

    ;; Process the actual history
    (dolist (msg (reverse actual-history)) ; Reverse to process oldest first, then nreverse at the end
      (let ((role (car msg))
            (raw-content (cdr msg)))
        (cond
         ((eq role 'user)
          (push `((role . "user") (parts . [((text . ,raw-content))])) gemini-contents))

         ((or (eq role 'assistant) (eq role 'model)) ; Internal 'assistant' or 'model'
          (if (stringp raw-content) ; Ensure raw-content is a string
              (if (string-match-p "<tool name=" raw-content)
                  ;; This model turn from history contained XML tool calls.
                  ;; Convert them to Gemini native functionCall parts.
                  (let* ((parsed-tool-calls (ai-auto-complete-tools-parse-response raw-content)) ; Parses XML to (name . params-alist) list
                         (function-call-parts
                          (when parsed-tool-calls
                            (mapcar (lambda (tc) ; tc is (tool-name . params-alist)
                                      `((functionCall . ((name . ,(car tc))
                                                         ;; params-alist (cdr tc) will be JSON encoded later
                                                         (args . ,(cdr tc))))))
                                    parsed-tool-calls))))
                    (if function-call-parts
                        (push `((role . "model") (parts . ,function-call-parts)) gemini-contents)
                      ;; If parsing failed or no tool calls, treat as text
                      (push `((role . "model") (parts . [((text . ,raw-content))])) gemini-contents)))
                ;; No XML tool calls, treat as plain text
                (push `((role . "model") (parts . [((text . ,raw-content))])) gemini-contents))
            ;; Fallback for non-string raw-content
            (progn
              (message "Warning: 'assistant'/'model' role in history has non-string raw-content: %S" raw-content)
              (push `((role . "model") (parts . [((text . "Error: unhandled model content"))])) gemini-contents))))

         ((eq role 'agent) ; Internal 'agent' role, maps to 'model'
           (let ((agent-text-response (if (and (consp raw-content) (stringp (car raw-content))) ; (agent-name . text)
                                         (cdr raw-content)
                                       (if (stringp raw-content) raw-content "")))) ; Fallback
            (if (string-match-p "<tool name=" agent-text-response)
                (let* ((parsed-tool-calls (ai-auto-complete-tools-parse-response agent-text-response))
                       (function-call-parts
                        (when parsed-tool-calls
                          (mapcar (lambda (tc)
                                    `((functionCall . ((name . ,(car tc))
                                                       (args . ,(cdr tc))))))
                                  parsed-tool-calls))))
                  (if function-call-parts
                      (push `((role . "model") (parts . ,function-call-parts)) gemini-contents)
                    (push `((role . "model") (parts . [((text . ,agent-text-response))])) gemini-contents)))
              (push `((role . "model") (parts . [((text . ,agent-text-response))])) gemini-contents))))

         ((eq role 'tool-result)
          (let* ((parsed-tool-results-list (ai-auto-complete-gemini--parse-internal-tool-result raw-content))) ; Returns a list of (name . output)
            (if (and parsed-tool-results-list (> (length parsed-tool-results-list) 0)) ; Ensure list is not nil and not empty
                (let ((function-response-parts
                       (mapcar (lambda (parsed-info)
                                 (let ((tool-name (car parsed-info))
                                       (tool-output (cdr parsed-info)))
                                   `((functionResponse . ((name . ,tool-name)
                                                          ;; Gemini expects 'response' to be a JSON object.
                                                          ;; If tool_output is just text, wrap it.
                                                          (response . ((output . ,tool-output))))))))
                               parsed-tool-results-list)))
                  (push `((role . "function")
                          (parts . ,function-response-parts)) ; Multiple functionResponse parts if needed
                        gemini-contents))
              ;; If parsed-tool-results-list is nil or empty, log a warning.
              ;; This means no function responses will be sent for this turn.
              (message "Warning: tool-result for Gemini was empty or unparseable, or yielded no results. Raw content: %s"
                       (if (stringp raw-content) (substring raw-content 0 (min (length raw-content) 100)) raw-content))))))))

    ;; Add the current user message (cleaned of system prompt)
    ;; But only if we're NOT in chat mode with existing history
    (when (and clean-user-context
               (not (string-empty-p clean-user-context))
               (not (and (boundp 'ai-auto-complete-chat-mode)
                        ai-auto-complete-chat-mode
                        actual-history
                        (> (length actual-history) 0))))
      (message "GEMINI-BUILD-CONTENTS: Adding user message: %s"
               (substring clean-user-context 0 (min 100 (length clean-user-context))))
      (push `((role . "user") (parts . [((text . ,clean-user-context))])) gemini-contents))

    (when (and (boundp 'ai-auto-complete-chat-mode)
               ai-auto-complete-chat-mode
               actual-history
               (> (length actual-history) 0))
      (message "GEMINI-BUILD-CONTENTS: In chat mode with history, NOT adding context as user message"))

    (message "GEMINI-BUILD-CONTENTS: Final contents count: %d" (length gemini-contents))
    (nreverse gemini-contents)))) ; nreverse to get chronological order for Gemini

;; Gemini provider implementation
(defun ai-auto-complete-gemini-provider (context history callback model system-prompt &optional agent-name)
  "Request completion from Gemini API with CONTEXT, HISTORY, MODEL, SYSTEM-PROMPT, and optional AGENT-NAME."
  (message "GEMINI-PROVIDER: FUNCTION CALLED! Context: %s"
           (substring context 0 (min 100 (length context))))
  (message "Requesting completion from Gemini API with model %s" model)
  (message "Using system prompt: %s" (substring system-prompt 0 (min 108 (length system-prompt))))
  (message "Passing history with %d messages" (length history))
  (when agent-name
    (message "Request is for agent: %s" agent-name))
  (if (string-empty-p ai-auto-complete-gemini-api-key)
      (progn
        (message "Gemini API key is not set")
        (funcall callback "ERROR: Gemini API key is not set. Please set ai-auto-complete-gemini-api-key."))
    (let* (;; Use the model directly - it should already be the correct model name
           ;; from ai-auto-complete-get-correct-model-name in the backend
           (url (format "https://generativelanguage.googleapis.com/v1beta/models/%s:generateContent?key=%s" ; Using v1beta for systemInstruction
                        model ai-auto-complete-gemini-api-key))
           ;; Model attributes (temperature, max-tokens, etc.)
           (config-attrs (ai-auto-complete-apply-model-attributes 'gemini model (make-hash-table :test 'equal)))
           (temperature (or (gethash "temperature" config-attrs) 0.7))
           (max-tokens (or (gethash "maxOutputTokens" config-attrs) 1024))
           (top-p (or (gethash "topP" config-attrs) 0.9))
           (top-k (or (gethash "topK" config-attrs) 40))
             (generation-config `((temperature . ,temperature)
                                 (maxOutputTokens . ,max-tokens)
                                 (topP . ,top-p)
                                 (topK . ,top-k)))
           ;; Prepare systemInstruction
           (gemini-system-instruction
            (when (and system-prompt (not (string-empty-p system-prompt)))
              `((parts . [((text . ,system-prompt))]))))
           ;; Prepare contents
           (gemini-contents (ai-auto-complete-gemini--build-contents history context))
           ;; Debug the contents structure
           (dummy (message "DEBUG: Gemini contents structure: %s"
                          (json-encode gemini-contents)))
           ;; TODO: Prepare 'tools' array if using native Gemini function calling
           ;; based on ai-auto-complete-get-tool-definitions
           (payload `((contents . ,gemini-contents)
                      (generationConfig . ,generation-config))))

      ;; Add systemInstruction to payload if it exists
      (when gemini-system-instruction
        (setq payload (append payload `((systemInstruction . ,gemini-system-instruction)))))

      ;; TODO: Add 'tools' and 'toolConfig' to payload if using native function calling

      (let ((data (json-encode payload)))
        (message "Gemini request payload (condensed): %s"
                 (substring data 0 (min 200 (length data))))
        (setq ai-auto-complete--pending-request t)
        (ai-auto-complete--update-mode-line)
        (request url :type "POST" :headers '(("Content-Type" . "application/json"))
                 :data data :parser 'json-read
               :success (lambda (&rest args)
                         (let* ((response-data (plist-get args :data)))
                           ;; Check if the response contains an error
                           (if (assoc 'error response-data)
                               (let* ((error-obj (cdr (assoc 'error response-data)))
                                      (error-msg (cdr (assoc 'message error-obj))))
                                 (message "Gemini API error message: %s" error-msg)
                                 (funcall callback (format "ERROR: %s" error-msg)))
                             ;; Normal response processing
                             (let* ((candidates (cdr (assoc 'candidates response-data)))
                                    (first-candidate (when (and candidates (> (length candidates) 0)) (aref candidates 0)))
                                    (content (when first-candidate (cdr (assoc 'content first-candidate))))
                                    (parts (when content (cdr (assoc 'parts content))))
                                    (first-part (when (and parts (> (length parts) 0)) (aref parts 0)))
                                    ;; Check for functionCall first
                                    (function-call-data (when first-part (cdr (assoc 'functionCall first-part))))
                                    (text-data (when first-part (cdr (assoc 'text first-part))))
                                    (final-response-text (or text-data "")))

                               ;; Pass the agent-name to the tools processing function
                               ;; If native function calling is used, function-call-data will be non-nil
                               (if function-call-data
                                   (progn
                                     ;; Construct the XML tool call string for the existing tools-processing system
                                     ;; This is a bridge until the whole system uses native tool calls.
                                     (let* ((tool-name (cdr (assoc 'name function-call-data)))
                                            (tool-args-json (json-encode (cdr (assoc 'args function-call-data))))
                                            (xml-tool-call (format "<tool name=\"%s\"><parameters>%s</parameters></tool>"
                                                                   tool-name tool-args-json)))
                                       (message "Gemini returned functionCall, processing as tool: %s" xml-tool-call)
                                       (ai-auto-complete-tools-process-response xml-tool-call callback agent-name)))
                                 ;; Else, it's a text response or a text response after a tool was already processed
                                 (if (and (boundp 'ai-auto-complete-tools-enabled)
                                          ai-auto-complete-tools-enabled
                                          (fboundp 'ai-auto-complete-tools-process-response)
                                          (string-match-p "<tool name=" final-response-text)) ; Check for manually inserted tool calls
                                     (progn
                                       (message "Processing Gemini text response for XML tools with agent-name: %s" (or agent-name "nil"))
                                       (ai-auto-complete-tools-process-response final-response-text callback agent-name))
                                   (funcall callback final-response-text)))))))
               :error (lambda (&rest args)
                       (let* ((request-error (plist-get args :error))
                              (response (plist-get args :response))
                              (error-data (plist-get args :data)))
                          (message "Error in Gemini provider - Full error details:")
                          (message "Error: %S" request-error)
                          (message "Response: %S" response)
                          (message "Error Data: %S" error-data)
                          (funcall callback (format "ERROR: %s\nResponse: %S\nData: %S"
                                                  request-error response error-data))))
                 )))))
;; Test functions for Gemini API

(defun ai-auto-complete-check-gemini-api-key ()
  "Check if the Gemini API key is valid by listing available models."
  (interactive)
  (message "Checking Gemini API key...")
  (if (string-empty-p ai-auto-complete-gemini-api-key)
      (message "ERROR: Gemini API key is not set")
    (let ((url (format "https://generativelanguage.googleapis.com/v1/models?key=%s"
                      ai-auto-complete-gemini-api-key)))
      (message "Requesting models list from: %s" url)
      (request url
               :type "GET"
               :parser 'json-read
               :success (cl-function
                         (lambda (&key data &allow-other-keys)
                           (message "Gemini API key is valid!")
                           (let* ((models (cdr (assoc 'models data)))
                                  (model-names (mapcar (lambda (model)
                                                        (cdr (assoc 'name model)))
                                                      models)))
                             (message "Available Gemini models: %S" model-names))))
               :error (cl-function
                       (lambda (&key error-thrown response &allow-other-keys)
                         (message "Gemini API key validation ERROR: %S" error-thrown)
                         (when response
                           (message "Response status: %s" (request-response-status-code response))
                           (message "Response data: %s" (request-response-data response)))
                         (message "Your Gemini API key appears to be invalid or has insufficient permissions.")))))
    t))

(defun ai-auto-complete-test-gemini-api ()
  "Test the Gemini API with a simple request and display detailed results."
  (interactive)
  (message "Testing Gemini API...")
  (message "API Key set: %s" (not (string-empty-p ai-auto-complete-gemini-api-key)))
  (let* ((model "gemini-2.0-flash-lite")
         (test-prompt "What is the capital of France?")
         ;; Use the model directly
         (url (format "https://generativelanguage.googleapis.com/v1/models/%s:generateContent?key=%s"
                     model
                     ai-auto-complete-gemini-api-key))
         (config-attrs (ai-auto-complete-apply-model-attributes 'gemini model (make-hash-table :test 'equal)))
         (temperature (or (and config-attrs (gethash "temperature" config-attrs)) 0.7))
         (max-tokens (or (and config-attrs (gethash "maxOutputTokens" config-attrs)) 1024))
         (top-p (or (and config-attrs (gethash "topP" config-attrs)) 0.9))
         (top-k (or (and config-attrs (gethash "topK" config-attrs)) 40))
         (generation-config `((temperature . ,temperature)
                             (maxOutputTokens . ,max-tokens)
                             (topP . ,top-p)
                             (topK . ,top-k)))
         (data (json-encode `((contents . [((role . "user")
                                         (parts . [((text . ,test-prompt))]))])
                            (generationConfig . ,generation-config)))))
    (message "Testing URL: %s" url)
    (message "Request data: %s" data)
    (request url
             :type "POST"
             :headers '(("Content-Type" . "application/json"))
             :data data
             :parser 'json-read
             :success (cl-function
                       (lambda (&key data &allow-other-keys)
                         (message "Gemini API test SUCCESS")
                         (message "Response data: %S" data)
                         (let* ((candidates (cdr (assoc 'candidates data)))
                                (first-candidate (aref candidates 0))
                                (content (cdr (assoc 'content first-candidate)))
                                (parts (cdr (assoc 'parts content)))
                                (first-part (aref parts 0))
                                (text (cdr (assoc 'text first-part))))
                           (message "Generated text: %s" text))))
             :error (cl-function
                     (lambda (&key error-thrown response data &allow-other-keys)
                       (message "Gemini API test ERROR: %S" error-thrown)
                       (when response
                         (message "Response status: %s" (request-response-status-code response))
                         (message "Response headers: %s" (request-response-headers response))
                         (message "Response data: %s" (request-response-data response)))
                       (message "Full error details: %S" (list :error error-thrown :response response :data data)))))))

(defun ai-auto-complete-test-gemini-model (model)
  "Test if a specific Gemini MODEL is available and working."
  (interactive
   (list (read-string "Enter Gemini model name to test: " "gemini-2.0-flash-lite")))
  (message "Testing Gemini model: %s" model)
  (if (string-empty-p ai-auto-complete-gemini-api-key)
      (message "ERROR: Gemini API key is not set")
    (let* (;; Use the model directly
           (url (format "https://generativelanguage.googleapis.com/v1/models/%s:generateContent?key=%s"
                      model
                      ai-auto-complete-gemini-api-key))
           (test-prompt "What is the capital of France?")
           (data (json-encode `((contents . [((role . "user")
                                           (parts . [((text . ,test-prompt))]))])
                              (generationConfig . ((temperature . 0.7)
                                                  (maxOutputTokens . 1024)
                                                  (topP . 0.9)
                                                  (topK . 40)))))))
      (message "Testing URL: %s" url)
      (request url
               :type "POST"
               :headers '(("Content-Type" . "application/json"))
               :data data
               :parser 'json-read
               :success (cl-function
                         (lambda (&key data &allow-other-keys)
                           (message "Model %s is working correctly!" model)
                           (message "Response received successfully")))
               :error (cl-function
                       (lambda (&key error-thrown response &allow-other-keys)
                         (message "Model test ERROR: %S" error-thrown)
                         (when response
                           (message "Response status: %s" (request-response-status-code response))
                           (message "Response data: %s" (request-response-data response)))
                         (message "Model %s appears to be invalid or inaccessible" model)))))))

(defun ai-auto-complete-test-gemini-chat ()
  "Test the Gemini API with a chat request format."
  (interactive)
  (message "Testing Gemini API with chat format...")
  (if (string-empty-p ai-auto-complete-gemini-api-key)
      (message "ERROR: Gemini API key is not set")
    (let* ((model "gemini-2.0-flash-lite")
           ;; Use the model directly
           (url (format "https://generativelanguage.googleapis.com/v1/models/%s:generateContent?key=%s"
                       model
                       ai-auto-complete-gemini-api-key))
           (system-prompt "You are a helpful AI assistant.")
           (user-message "What is the capital of France?")
           ;; Create a chat-like message structure - prepend system prompt to user message
           (messages (list
                      `((role . "user")
                        (parts . [((text . ,(concat "System: " system-prompt "\n\nUser: " user-message)))]))))
           (data (json-encode `((contents . ,(vconcat [] messages))
                              (generationConfig . ((temperature . 0.7)
                                                  (maxOutputTokens . 1024)
                                                  (topP . 0.9)
                                                  (topK . 40)))))))
      (message "Testing URL: %s" url)
      (message "Request data: %s" data)
      (request url
               :type "POST"
               :headers '(("Content-Type" . "application/json"))
               :data data
               :parser 'json-read
               :success (cl-function
                         (lambda (&key data &allow-other-keys)
                           (message "Gemini chat test SUCCESS")
                           (message "Response data: %S" data)
                           (let* ((candidates (cdr (assoc 'candidates data)))
                                  (first-candidate (aref candidates 0))
                                  (content (cdr (assoc 'content first-candidate)))
                                  (parts (cdr (assoc 'parts content)))
                                  (first-part (aref parts 0))
                                  (text (cdr (assoc 'text first-part))))
                             (message "Generated text: %s" text))))
               :error (cl-function
                       (lambda (&key error-thrown response data &allow-other-keys)
                         (message "Gemini chat test ERROR: %S" error-thrown)
                         (when response
                           (message "Response status: %s" (request-response-status-code response))
                           (message "Response headers: %s" (request-response-headers response))
                           (message "Response data: %s" (request-response-data response)))
                         (message "Full error details: %S" (list :error error-thrown :response response :data data))))))))

(defun ai-auto-complete-debug-gemini-chat-history ()
  "Debug the Gemini chat history format."
  (interactive)
  (if (not (and (boundp 'ai-auto-complete--chat-history)
                (not (null ai-auto-complete--chat-history))))
      (message "No chat history available. Please start a chat first.")
    (let* ((model "gemini-2.0-flash-lite")
           (system-prompt "You are a helpful AI assistant.")
           (context "Hello")
           (messages nil))
      ;; Add current message with system prompt prepended
      (push `((role . "user")
             (parts . [((text . ,(concat "System: " system-prompt "\n\nUser: " context)))])) messages)
      ;; Add history messages (oldest to newest)
      (dolist (msg ai-auto-complete--chat-history) ;; No need to reverse, we want oldest first
        (let ((role (cond ((eq (car msg) 'user) "user")
                         ((eq (car msg) 'agent) "model")
                         ((eq (car msg) 'tool-result) "model")
                         (t "model")))
              (content (cond
                        ((eq (car msg) 'agent) (cdr (cdr msg))) ; Extract the actual message content from agent response
                        ((eq (car msg) 'tool-result) (format "Tool Results: %s" (cdr msg))) ; Format tool results
                        (t (cdr msg)))))
          (message "Chat history entry - Role: %s, Content: %s" role
                   (if (stringp content)
                       (substring content 0 (min 30 (length content)))
                     "<non-string content>"))
          (push `((role . ,role)
                 (parts . [((text . ,content))])) messages)))

      (let* ((url (format "https://generativelanguage.googleapis.com/v1/models/%s:generateContent?key=%s"
                        model
                        ai-auto-complete-gemini-api-key))
             (data (json-encode `((contents . ,(vconcat [] (reverse messages)))
                                (generationConfig . ((temperature . 0.7)
                                                    (maxOutputTokens . 1024)
                                                    (topP . 0.9)
                                                    (topK . 40)))))))
        (message "Chat history debug - URL: %s" url)
        (message "Chat history debug - Request data: %s" data)
        (message "Chat history debug - Total messages: %d" (length messages))
        (request url
                 :type "POST"
                 :headers '(("Content-Type" . "application/json"))
                 :data data
                 :parser 'json-read
                 :success (cl-function
                           (lambda (&key data &allow-other-keys)
                             (message "Chat history debug - SUCCESS")
                             (message "Chat history debug - Response received successfully")))
                 :error (cl-function
                         (lambda (&key error-thrown response data &allow-other-keys)
                           (message "Chat history debug - ERROR: %S" error-thrown)
                           (when response
                             (message "Chat history debug - Response status: %s" (request-response-status-code response))
                             (message "Chat history debug - Response data: %s" (request-response-data response)))
                           (message "Chat history debug - Full error details: %S" (list :error error-thrown :response response :data data)))))))))

(defun ai-auto-complete-test-gemini-model-variants ()
  "Test different Gemini model name variants to find which ones work."
  (interactive)
  (message "Testing different Gemini model variants...")
  (let ((models '("gemini-2.0-flash-lite"
                  "gemini-2.0-flash"
                  "gemini-2.0-pro"
                  "gemini-2.5-flash-preview"
                  "gemini-2.5-pro-preview-03-25"
                  "gemini-2.5-pro-exp-03-25"
                  "gemini-pro"
                  "gemini-pro-vision"
                  "gemini-1.5-pro"
                  "gemini-1.5-flash")))
    (dolist (model models)
      (message "Testing model variant: %s" model)
      (let* (;; Use the model directly
             (url (format "https://generativelanguage.googleapis.com/v1/models/%s:generateContent?key=%s"
                        model
                        ai-auto-complete-gemini-api-key))
             (test-prompt "What is the capital of France?")
             (data (json-encode `((contents . [((role . "user")
                                             (parts . [((text . ,test-prompt))]))])
                                (generationConfig . ((temperature . 0.7)
                                                    (maxOutputTokens . 1024)
                                                    (topP . 0.9)
                                                    (topK . 40)))))))
        (request url
                 :type "POST"
                 :headers '(("Content-Type" . "application/json"))
                 :data data
                 :parser 'json-read
                 :success (cl-function
                           (lambda (&key data &allow-other-keys)
                             (message "Model %s: SUCCESS" model)))
                 :error (cl-function
                         (lambda (&key error-thrown response &allow-other-keys)
                           (message "Model %s: ERROR - %s" model error-thrown)
                           (when response
                             (message "Model %s: Status %s" model (request-response-status-code response))))))))))

(defun ai-auto-complete-test-gemini-contents-fix ()
  "Test the fixed ai-auto-complete-gemini--build-contents function."
  (interactive)
  (message "Testing Gemini contents fix...")

  ;; Test case 1: Normal chat context with system prompt concatenated
  (let* ((test-context "You are a general-purpose conversational AI assistant. Your role is to be helpful, harmless, and honest in all interactions.\n\nConversation history:\nUser: @chat ARe aliens real?\n\n")
         (test-history '((user . "Hello") (agent . ("chat" . "Hi there!"))))
         (ai-auto-complete-chat-mode t)
         (ai-auto-complete--chat-history test-history)
         (result (ai-auto-complete-gemini--build-contents '() test-context)))
    (message "Test 1 - Chat context with system prompt:")
    (message "Input context: %s" (substring test-context 0 (min 100 (length test-context))))
    (message "Result: %s" (json-encode result)))

  ;; Test case 2: Simple user message
  (let* ((test-context "Are aliens real?")
         (test-history '((user . "Hello") (agent . ("chat" . "Hi there!"))))
         (ai-auto-complete-chat-mode t)
         (ai-auto-complete--chat-history test-history)
         (result (ai-auto-complete-gemini--build-contents test-history test-context)))
    (message "Test 2 - Simple user message with history:")
    (message "Input context: %s" test-context)
    (message "Result: %s" (json-encode result)))

  ;; Test case 3: Non-chat mode
  (let* ((test-context "Complete this code")
         (test-history '())
         (ai-auto-complete-chat-mode nil)
         (result (ai-auto-complete-gemini--build-contents test-history test-context)))
    (message "Test 3 - Non-chat mode:")
    (message "Input context: %s" test-context)
    (message "Result: %s" (json-encode result))))

;; Register the provider
(ai-auto-complete-register-provider 'gemini #'ai-auto-complete-gemini-provider)

(provide 'providers/gemini)
;;; gemini.el ends here
